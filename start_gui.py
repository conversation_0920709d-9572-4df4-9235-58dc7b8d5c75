#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMComic GUI 启动脚本
检查依赖并启动GUI程序
"""

import sys
import subprocess
import importlib.util


def check_package(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None


def install_package(package_name):
    """安装包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"{package_name} 安装成功！")
        return True
    except subprocess.CalledProcessError:
        print(f"安装 {package_name} 失败！")
        return False


def check_and_install_dependencies():
    """检查并安装依赖"""
    dependencies = {
        'PyQt5': 'PyQt5>=5.15.0',
        'jmcomic': 'jmcomic>=2.6.0'
    }
    
    missing_packages = []
    
    print("检查依赖包...")
    for package, pip_name in dependencies.items():
        if not check_package(package):
            print(f"❌ {package} 未安装")
            missing_packages.append(pip_name)
        else:
            print(f"✅ {package} 已安装")
    
    if missing_packages:
        print(f"\n发现 {len(missing_packages)} 个缺失的依赖包")
        
        # 询问用户是否自动安装
        response = input("是否自动安装缺失的依赖包？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            print("\n开始安装依赖包...")
            for package in missing_packages:
                if not install_package(package):
                    print(f"\n安装失败！请手动运行: pip install {package}")
                    return False
            print("\n所有依赖包安装完成！")
        else:
            print("\n请手动安装依赖包:")
            for package in missing_packages:
                print(f"  pip install {package}")
            return False
    
    return True


def main():
    """主函数"""
    print("=" * 50)
    print("JMComic GUI 漫画下载器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("\n❌ 依赖检查失败，程序退出")
        sys.exit(1)
    
    # 启动GUI
    print("\n🚀 启动GUI程序...")
    try:
        from jmcomic_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保 jmcomic_gui.py 文件在当前目录")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
