name: Bug
title: '[Bug] '
description: Bug反馈
labels: [ 'bug' ]

body:
  - type: checkboxes
    attributes:
      label: 请确认注意事项
      options:
        - label: 你已经在GitHub Issues和Discussions里搜索过类似的问题，并且没找到想要的答案。
          required: true
        - label: 你使用的是最新的jmcomic版本
          required: true
        - label: 非GitHub Actions类问题（此类问题请专门到置顶的Issue反馈）
          required: true

  - type: dropdown
    attributes:
      label: 发生Bug时，你的使用方式？
      multiple: true
      options:
        - Python代码
        - jmcomic命令行
    validations:
      required: true

  - type: dropdown
    attributes:
      label: 你是否在禁漫官方网页或APP，验证过相应的功能是正常的？
      multiple: true
      options:
        - 测试过，禁漫官方功能正常
        - 我要反馈的bug无需验证/不方便验证
    validations:
      required: true

  - type: textarea
    attributes:
      label: 代码/option配置
      placeholder: |
        - 如果是运行代码，请提供核心代码片段
        - 如果有自定义option配置，请提供配置内容
        - 如果是GitHub Actions，请提供运行记录的URL

  - type: textarea
    attributes:
      label: 描述bug现象（你预期的结果，与实际结果的差别）
      placeholder: |
        1. 预期结果...
        2. 差别...

  - type: textarea
    attributes:
      label: 其他可提供的信息
      placeholder: 截图/程序输出日志等

