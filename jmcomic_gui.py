#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMComic GUI下载器
基于PyQt5和jmcomic库的简单图形界面下载工具
"""

import sys
import os
import threading
import traceback
import json
from pathlib import Path
from typing import Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QProgressBar, QGroupBox, QCheckBox, QSpinBox, QComboBox,
    QMessageBox, QSplitter, QFrame, QMenuBar, QAction
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

# 检查jmcomic是否已安装
try:
    import jmcomic
    from jmcomic import JmOption
    JMCOMIC_AVAILABLE = True
except ImportError:
    JMCOMIC_AVAILABLE = False


class DownloadWorker(QThread):
    """下载工作线程"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    download_finished = pyqtSignal(bool, str)  # 下载完成信号 (成功, 消息)
    
    def __init__(self, album_ids, download_dir, options):
        super().__init__()
        self.album_ids = album_ids
        self.download_dir = download_dir
        self.options = options
        self.is_cancelled = False
    
    def run(self):
        """执行下载任务"""
        try:
            if not JMCOMIC_AVAILABLE:
                self.download_finished.emit(False, "jmcomic库未安装，请先安装：pip install jmcomic")
                return
            
            # 创建下载配置
            option_dict = {
                'dir_rule': {
                    'base_dir': self.download_dir,
                    'rule': self.options.get('dir_rule', 'Bd_Aauthor_Atitle_Pindex')
                },
                'client': {
                    'impl': self.options.get('client_impl', 'api'),
                    'retry_times': self.options.get('retry_times', 5)
                },
                'download': {
                    'cache': self.options.get('cache_enabled', True),
                    'image': {
                        'suffix': self.options.get('image_format', None),
                        'decode': self.options.get('decode_enabled', True)
                    },
                    'threading': {
                        'image': self.options.get('concurrent_images', 30),
                        'photo': self.options.get('concurrent_photos', None)
                    }
                }
            }
            
            # 创建option对象
            option = JmOption.construct(option_dict)
            
            # 下载每个漫画
            total_albums = len(self.album_ids)
            for i, album_id in enumerate(self.album_ids):
                if self.is_cancelled:
                    break
                
                self.progress_updated.emit(f"正在下载漫画 {album_id} ({i+1}/{total_albums})...")
                
                try:
                    # 下载漫画
                    jmcomic.download_album(album_id, option)
                    self.progress_updated.emit(f"漫画 {album_id} 下载完成")
                except Exception as e:
                    error_msg = f"下载漫画 {album_id} 失败: {str(e)}"
                    self.progress_updated.emit(error_msg)
                    print(f"Error downloading {album_id}: {e}")
            
            if not self.is_cancelled:
                self.download_finished.emit(True, f"所有漫画下载完成！共下载 {total_albums} 本漫画")
            else:
                self.download_finished.emit(False, "下载已取消")
                
        except Exception as e:
            error_msg = f"下载过程中发生错误: {str(e)}\n{traceback.format_exc()}"
            self.download_finished.emit(False, error_msg)
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True


class JMComicGUI(QMainWindow):
    """JMComic GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.download_worker = None
        self.init_ui()
        self.load_config()  # 加载保存的配置
        self.check_dependencies()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JMComic 漫画下载器")
        self.setGeometry(100, 100, 800, 600)

        # 创建菜单栏
        self.create_menu_bar()

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分：输入和配置
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        
        # 漫画ID输入区域
        self.create_input_section(top_layout)
        
        # 下载配置区域
        self.create_config_section(top_layout)
        
        # 下载控制区域
        self.create_control_section(top_layout)
        
        splitter.addWidget(top_widget)
        
        # 下半部分：日志输出
        self.create_log_section(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 200])

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        # 保存配置
        save_config_action = QAction('保存配置', self)
        save_config_action.setShortcut('Ctrl+S')
        save_config_action.triggered.connect(self.save_config)
        file_menu.addAction(save_config_action)

        # 加载配置
        load_config_action = QAction('加载配置', self)
        load_config_action.setShortcut('Ctrl+O')
        load_config_action.triggered.connect(self.load_config)
        file_menu.addAction(load_config_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具')

        # 清除日志
        clear_log_action = QAction('清除日志', self)
        clear_log_action.triggered.connect(self.log_text.clear)
        tools_menu.addAction(clear_log_action)

        # 打开下载目录
        open_dir_action = QAction('打开下载目录', self)
        open_dir_action.triggered.connect(self.open_download_directory)
        tools_menu.addAction(open_dir_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助')

        # 使用说明
        help_action = QAction('使用说明', self)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)

        # 关于
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_input_section(self, parent_layout):
        """创建输入区域"""
        group = QGroupBox("漫画信息")
        layout = QVBoxLayout(group)
        
        # 漫画ID输入
        id_layout = QHBoxLayout()
        id_layout.addWidget(QLabel("漫画ID:"))
        self.album_id_input = QLineEdit()
        self.album_id_input.setPlaceholderText("输入漫画ID，多个ID用逗号分隔，例如: 422866, 123456")
        id_layout.addWidget(self.album_id_input)
        layout.addLayout(id_layout)
        
        # 下载目录选择
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("下载目录:"))
        self.download_dir_input = QLineEdit()
        self.download_dir_input.setText(str(Path.home() / "Downloads" / "JMComic"))
        dir_layout.addWidget(self.download_dir_input)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.browse_button)
        layout.addLayout(dir_layout)
        
        parent_layout.addWidget(group)
    
    def create_config_section(self, parent_layout):
        """创建配置区域"""
        group = QGroupBox("下载配置")
        layout = QVBoxLayout(group)

        # 第一行：客户端类型和图片格式
        row1 = QHBoxLayout()

        # 客户端类型
        row1.addWidget(QLabel("客户端类型:"))
        self.client_combo = QComboBox()
        self.client_combo.addItems(["api (移动端)", "html (网页端)"])
        self.client_combo.setCurrentText("api (移动端)")
        self.client_combo.setToolTip("移动端：不限IP，兼容性好\n网页端：限制IP地区但效率高")
        row1.addWidget(self.client_combo)

        # 图片格式
        row1.addWidget(QLabel("图片格式:"))
        self.format_combo = QComboBox()
        self.format_combo.addItems(["原格式", ".jpg", ".png", ".webp"])
        self.format_combo.setToolTip("选择保存的图片格式\n原格式：保持原始格式不转换")
        row1.addWidget(self.format_combo)

        layout.addLayout(row1)

        # 第二行：并发设置
        row2 = QHBoxLayout()

        # 图片并发数
        row2.addWidget(QLabel("图片并发数:"))
        self.image_concurrent_spin = QSpinBox()
        self.image_concurrent_spin.setRange(1, 100)
        self.image_concurrent_spin.setValue(30)
        self.image_concurrent_spin.setToolTip("同时下载的图片数量\n数值越大下载越快，但对服务器压力越大")
        row2.addWidget(self.image_concurrent_spin)

        # 章节并发数
        row2.addWidget(QLabel("章节并发数:"))
        self.photo_concurrent_spin = QSpinBox()
        self.photo_concurrent_spin.setRange(1, 50)
        self.photo_concurrent_spin.setValue(16)
        self.photo_concurrent_spin.setToolTip("同时下载的章节数量")
        row2.addWidget(self.photo_concurrent_spin)

        layout.addLayout(row2)

        # 第三行：高级选项
        row3 = QHBoxLayout()

        # 启用缓存
        self.cache_checkbox = QCheckBox("启用缓存")
        self.cache_checkbox.setChecked(True)
        self.cache_checkbox.setToolTip("如果文件已存在则跳过下载")
        row3.addWidget(self.cache_checkbox)

        # 图片解码
        self.decode_checkbox = QCheckBox("图片解码")
        self.decode_checkbox.setChecked(True)
        self.decode_checkbox.setToolTip("还原JM的混淆图片")
        row3.addWidget(self.decode_checkbox)

        # 重试次数
        row3.addWidget(QLabel("重试次数:"))
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(1, 20)
        self.retry_spin.setValue(5)
        self.retry_spin.setToolTip("下载失败时的重试次数")
        row3.addWidget(self.retry_spin)

        layout.addLayout(row3)

        # 第四行：文件夹规则
        row4 = QHBoxLayout()
        row4.addWidget(QLabel("文件夹规则:"))
        self.dir_rule_combo = QComboBox()
        self.dir_rule_combo.addItems([
            "Bd_Aauthor_Atitle_Pindex (作者_标题_页码)",
            "Bd_Atitle_Pindex (标题_页码)",
            "Bd_Aid_Pindex (ID_页码)",
            "Bd_Pname (章节名)"
        ])
        self.dir_rule_combo.setToolTip("设置下载文件的文件夹结构")
        row4.addWidget(self.dir_rule_combo)

        layout.addLayout(row4)

        parent_layout.addWidget(group)
    
    def create_control_section(self, parent_layout):
        """创建控制区域"""
        layout = QHBoxLayout()
        
        # 下载按钮
        self.download_button = QPushButton("开始下载")
        self.download_button.clicked.connect(self.start_download)
        self.download_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        layout.addWidget(self.download_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消下载")
        self.cancel_button.clicked.connect(self.cancel_download)
        self.cancel_button.setEnabled(False)
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        layout.addWidget(self.cancel_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        parent_layout.addLayout(layout)
    
    def create_log_section(self, splitter):
        """创建日志区域"""
        group = QGroupBox("下载日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 清除日志按钮
        clear_button = QPushButton("清除日志")
        clear_button.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_button)
        
        splitter.addWidget(group)

    def check_dependencies(self):
        """检查依赖库"""
        if not JMCOMIC_AVAILABLE:
            self.log("警告: jmcomic库未安装！")
            self.log("请运行以下命令安装: pip install jmcomic")
            self.download_button.setEnabled(False)
        else:
            self.log("jmcomic库已就绪")

    def browse_directory(self):
        """浏览选择下载目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择下载目录", self.download_dir_input.text()
        )
        if directory:
            self.download_dir_input.setText(directory)

    def log(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def parse_album_ids(self, text):
        """解析漫画ID列表"""
        if not text.strip():
            return []

        # 分割并清理ID
        ids = []
        for item in text.split(','):
            item = item.strip()
            if item:
                # 移除可能的前缀（如 'JM'）
                if item.lower().startswith('jm'):
                    item = item[2:]
                # 验证是否为数字
                if item.isdigit():
                    ids.append(item)
                else:
                    self.log(f"警告: 无效的漫画ID '{item}'，已跳过")

        return ids

    def get_download_options(self):
        """获取下载配置选项"""
        # 客户端类型
        client_text = self.client_combo.currentText()
        client_impl = 'api' if 'api' in client_text else 'html'

        # 图片格式
        format_text = self.format_combo.currentText()
        image_format = None if format_text == "原格式" else format_text

        # 文件夹规则
        dir_rule_text = self.dir_rule_combo.currentText()
        dir_rule_map = {
            "Bd_Aauthor_Atitle_Pindex (作者_标题_页码)": "Bd_Aauthor_Atitle_Pindex",
            "Bd_Atitle_Pindex (标题_页码)": "Bd_Atitle_Pindex",
            "Bd_Aid_Pindex (ID_页码)": "Bd_Aid_Pindex",
            "Bd_Pname (章节名)": "Bd_Pname"
        }
        dir_rule = dir_rule_map.get(dir_rule_text, "Bd_Aauthor_Atitle_Pindex")

        return {
            'client_impl': client_impl,
            'image_format': image_format,
            'concurrent_images': self.image_concurrent_spin.value(),
            'concurrent_photos': self.photo_concurrent_spin.value(),
            'cache_enabled': self.cache_checkbox.isChecked(),
            'decode_enabled': self.decode_checkbox.isChecked(),
            'retry_times': self.retry_spin.value(),
            'dir_rule': dir_rule
        }

    def start_download(self):
        """开始下载"""
        # 验证输入
        album_ids = self.parse_album_ids(self.album_id_input.text())
        if not album_ids:
            QMessageBox.warning(self, "输入错误", "请输入有效的漫画ID")
            return

        download_dir = self.download_dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "输入错误", "请选择下载目录")
            return

        # 创建下载目录
        try:
            Path(download_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建下载目录: {e}")
            return

        # 获取配置选项
        options = self.get_download_options()

        # 开始下载
        self.log(f"开始下载 {len(album_ids)} 本漫画: {', '.join(album_ids)}")
        self.log(f"下载目录: {download_dir}")
        self.log(f"客户端类型: {options['client_impl']}")

        # 创建并启动下载线程
        self.download_worker = DownloadWorker(album_ids, download_dir, options)
        self.download_worker.progress_updated.connect(self.log)
        self.download_worker.download_finished.connect(self.on_download_finished)
        self.download_worker.start()

        # 更新UI状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条

    def cancel_download(self):
        """取消下载"""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.cancel()
            self.log("正在取消下载...")

    def on_download_finished(self, success, message):
        """下载完成回调"""
        self.log(message)

        # 更新UI状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        # 显示完成消息
        if success:
            QMessageBox.information(self, "下载完成", message)
        else:
            QMessageBox.warning(self, "下载失败", message)

        # 清理工作线程
        self.download_worker = None

    def save_config(self):
        """保存配置到文件"""
        config = {
            'download_dir': self.download_dir_input.text(),
            'client_type': self.client_combo.currentIndex(),
            'image_format': self.format_combo.currentIndex(),
            'image_concurrent': self.image_concurrent_spin.value(),
            'photo_concurrent': self.photo_concurrent_spin.value(),
            'cache_enabled': self.cache_checkbox.isChecked(),
            'decode_enabled': self.decode_checkbox.isChecked(),
            'retry_times': self.retry_spin.value(),
            'dir_rule': self.dir_rule_combo.currentIndex()
        }

        config_file = Path("jmcomic_gui_config.json")
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.log("配置已保存")
        except Exception as e:
            self.log(f"保存配置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        config_file = Path("jmcomic_gui_config.json")
        if not config_file.exists():
            return

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 应用配置
            if 'download_dir' in config:
                self.download_dir_input.setText(config['download_dir'])
            if 'client_type' in config:
                self.client_combo.setCurrentIndex(config['client_type'])
            if 'image_format' in config:
                self.format_combo.setCurrentIndex(config['image_format'])
            if 'image_concurrent' in config:
                self.image_concurrent_spin.setValue(config['image_concurrent'])
            if 'photo_concurrent' in config:
                self.photo_concurrent_spin.setValue(config['photo_concurrent'])
            if 'cache_enabled' in config:
                self.cache_checkbox.setChecked(config['cache_enabled'])
            if 'decode_enabled' in config:
                self.decode_checkbox.setChecked(config['decode_enabled'])
            if 'retry_times' in config:
                self.retry_spin.setValue(config['retry_times'])
            if 'dir_rule' in config:
                self.dir_rule_combo.setCurrentIndex(config['dir_rule'])

            self.log("配置已加载")
        except Exception as e:
            self.log(f"加载配置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 自动保存配置
        self.save_config()

        # 如果有下载任务在运行，询问是否取消
        if self.download_worker and self.download_worker.isRunning():
            reply = QMessageBox.question(
                self, '确认退出',
                '下载任务正在进行中，是否要取消下载并退出？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.download_worker.cancel()
                self.download_worker.wait(3000)  # 等待3秒
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def open_download_directory(self):
        """打开下载目录"""
        download_dir = self.download_dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "提示", "请先设置下载目录")
            return

        if not Path(download_dir).exists():
            QMessageBox.warning(self, "提示", "下载目录不存在")
            return

        # 根据操作系统打开文件夹
        import platform
        import subprocess
        system = platform.system()

        try:
            if system == "Windows":
                os.startfile(download_dir)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", download_dir])
            else:  # Linux
                subprocess.run(["xdg-open", download_dir])
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开目录: {e}")

    def show_help(self):
        """显示使用说明"""
        help_text = """JMComic 漫画下载器 使用说明

1. 输入漫画ID
   - 在"漫画ID"输入框中输入要下载的漫画ID
   - 支持多个ID，用逗号分隔
   - 例如：422866, 123456, 789012

2. 选择下载目录
   - 点击"浏览..."按钮选择保存位置
   - 或直接输入目录路径

3. 配置下载选项
   - 客户端类型：移动端(api)或网页端(html)
   - 图片格式：选择保存格式
   - 并发数：调整下载速度
   - 其他高级选项

4. 开始下载
   - 点击"开始下载"按钮
   - 在日志区域查看进度
   - 可随时取消下载

快捷键：
- Ctrl+S: 保存配置
- Ctrl+O: 加载配置
- Ctrl+Q: 退出程序

注意事项：
- 确保网络连接正常
- 遵守相关法律法规
- 仅用于学习和研究目的"""

        QMessageBox.information(self, "使用说明", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """JMComic 漫画下载器 v1.0.0

基于 PyQt5 和 jmcomic 库开发的图形界面下载工具

特性：
• 简洁直观的用户界面
• 支持批量下载
• 可配置下载选项
• 实时进度显示
• 自动保存配置

技术栈：
• Python 3.7+
• PyQt5
• jmcomic 库

开源协议：MIT License

⚠️ 免责声明：
本软件仅供学习和研究使用，请遵守相关法律法规。
使用本软件所产生的任何后果由用户自行承担。"""

        QMessageBox.about(self, "关于", about_text)


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("JMComic 漫画下载器")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("JMComic GUI")

    # 创建并显示主窗口
    window = JMComicGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
