首页
=====================================



> jmcomic库封装了一套可用于爬取禁漫的Python API.
>
> 你可以通过简单的几行Python代码，访问禁漫的接口，以及下载禁漫的本子。
> 
> [查看项目更新计划](TODO.md)



## 入门

- [快速上手(GitHub README)](https://github.com/hect0x7/JMComic-Crawler-Python/tree/master?tab=readme-ov-file#%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B)
- [常用类和方法演示](tutorial/0_common_usage.md)
- [option配置以及插件写法](./option_file_syntax.md)



## 特殊用法教程
- [使用GitHub Actions下载本子](./tutorial/1_github_actions.md)
- [使用GitHub Actions导出收藏夹](tutorial/10_export_favorites.md)
- [命令行用法教程](tutorial/2_command_line.md)
- [测试你的ip能访问哪些禁漫域名](tutorial/8_pick_domain.md)



## 核心机制
- [下载过滤器机制](tutorial/5_filter.md)
- [插件机制](tutorial/6_plugin.md)



## 自定义
- [下载文件夹名](tutorial/9_custom_download_dir_name.md)
- [日志](tutorial/11_log_custom.md)
- [模块](tutorial/4_module_custom.md)

