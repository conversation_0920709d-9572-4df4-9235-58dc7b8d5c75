#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简美观的JMComic下载器
使用customtkinter打造现代化界面
"""

import sys
import os
import threading
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import customtkinter as ctk
except ImportError:
    print("正在安装 customtkinter...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "customtkinter"])
    import customtkinter as ctk

try:
    import jmcomic
except ImportError:
    print("请先安装jmcomic: pip install jmcomic")
    sys.exit(1)

# 设置外观模式和颜色主题
ctk.set_appearance_mode("light")  # "light" 或 "dark"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"


class JMComicDownloader:
    def __init__(self):
        self.root = ctk.CTk()
        self.download_thread = None
        self.is_downloading = False
        
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("JMComic Downloader")
        self.root.geometry("600x500")
        self.root.minsize(500, 400)
        
        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="JMComic Downloader",
            font=ctk.CTkFont(size=32, weight="bold")
        )
        title_label.pack(pady=(0, 10))
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="简约 · 现代 · 高效",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 30))
        
        # 输入区域
        input_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        input_frame.pack(fill="x", pady=(0, 20))
        
        # 漫画ID输入
        id_label = ctk.CTkLabel(
            input_frame,
            text="漫画ID",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        id_label.pack(anchor="w", padx=25, pady=(25, 5))
        
        self.id_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="输入漫画ID，多个用逗号分隔 (例: 422866, 123456)",
            height=45,
            font=ctk.CTkFont(size=13)
        )
        self.id_entry.pack(fill="x", padx=25, pady=(0, 20))
        
        # 下载目录
        dir_label = ctk.CTkLabel(
            input_frame,
            text="下载目录",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        dir_label.pack(anchor="w", padx=25, pady=(0, 5))
        
        # 目录选择框架
        dir_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        dir_frame.pack(fill="x", padx=25, pady=(0, 25))
        
        self.dir_entry = ctk.CTkEntry(
            dir_frame,
            height=45,
            font=ctk.CTkFont(size=13)
        )
        self.dir_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.dir_entry.insert(0, str(Path.home() / "Downloads" / "JMComic"))
        
        browse_btn = ctk.CTkButton(
            dir_frame,
            text="浏览",
            width=80,
            height=45,
            command=self.browse_directory
        )
        browse_btn.pack(side="right")
        
        # 按钮区域
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=(0, 20))
        
        self.download_btn = ctk.CTkButton(
            button_frame,
            text="开始下载",
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=self.start_download
        )
        self.download_btn.pack(side="left", padx=(0, 10))
        
        self.cancel_btn = ctk.CTkButton(
            button_frame,
            text="取消",
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color="gray",
            hover_color="darkgray",
            command=self.cancel_download,
            state="disabled"
        )
        self.cancel_btn.pack(side="left")
        
        # 进度条
        self.progress = ctk.CTkProgressBar(main_frame, height=8)
        self.progress.pack(fill="x", pady=(0, 20))
        self.progress.set(0)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="准备就绪",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=(0, 10))
        
        # 日志区域
        log_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        log_frame.pack(fill="both", expand=True)
        
        log_title = ctk.CTkLabel(
            log_frame,
            text="下载日志",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_title.pack(anchor="w", padx=20, pady=(20, 10))
        
        # 日志文本框
        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=12),
            corner_radius=10
        )
        self.log_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 初始化日志
        self.log("🚀 JMComic Downloader 已启动")
        self.log("💡 请输入漫画ID开始下载")
        
    def browse_directory(self):
        """浏览选择目录"""
        directory = filedialog.askdirectory(title="选择下载目录")
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert("end", f"{message}\n")
        self.log_text.see("end")
        self.root.update_idletasks()
    
    def update_status(self, text):
        """更新状态"""
        self.status_label.configure(text=text)
        self.root.update_idletasks()
    
    def start_download(self):
        """开始下载"""
        # 验证输入
        id_text = self.id_entry.get().strip()
        if not id_text:
            messagebox.showerror("错误", "请输入漫画ID")
            return
        
        # 解析ID
        album_ids = []
        for id_str in id_text.split(','):
            id_str = id_str.strip()
            if id_str:
                if id_str.upper().startswith('JM'):
                    id_str = id_str[2:]
                album_ids.append(id_str)
        
        if not album_ids:
            messagebox.showerror("错误", "请输入有效的漫画ID")
            return
        
        download_dir = self.dir_entry.get().strip()
        if not download_dir:
            messagebox.showerror("错误", "请选择下载目录")
            return
        
        # 创建目录
        try:
            Path(download_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法创建目录: {e}")
            return
        
        # 更新UI状态
        self.is_downloading = True
        self.download_btn.configure(state="disabled")
        self.cancel_btn.configure(state="normal")
        self.progress.set(0)
        
        self.log(f"🚀 开始下载 {len(album_ids)} 本漫画")
        self.log(f"📁 保存到: {download_dir}")
        self.update_status("正在下载...")
        
        # 启动下载线程
        self.download_thread = threading.Thread(
            target=self.download_worker,
            args=(album_ids, download_dir),
            daemon=True
        )
        self.download_thread.start()
    
    def download_worker(self, album_ids, download_dir):
        """下载工作线程"""
        try:
            total = len(album_ids)
            for i, album_id in enumerate(album_ids, 1):
                if not self.is_downloading:
                    break
                
                self.root.after(0, lambda: self.log(f"📖 下载漫画 {album_id} ({i}/{total})"))
                self.root.after(0, lambda: self.update_status(f"下载中 {i}/{total}"))
                self.root.after(0, lambda: self.progress.set((i-1) / total))
                
                # 创建下载配置
                option = jmcomic.JmOption.construct({
                    'dir_rule': {
                        'base_dir': download_dir,
                        'rule': 'Bd_Aauthor_Atitle_Pindex'
                    }
                })
                
                # 下载漫画
                jmcomic.download_album(album_id, option)
                
                self.root.after(0, lambda: self.log(f"✅ 漫画 {album_id} 下载完成"))
                self.root.after(0, lambda: self.progress.set(i / total))
            
            if self.is_downloading:
                self.root.after(0, lambda: self.log("🎉 所有下载任务完成！"))
                self.root.after(0, lambda: self.update_status("下载完成"))
            else:
                self.root.after(0, lambda: self.log("⏹️ 下载已取消"))
                self.root.after(0, lambda: self.update_status("已取消"))
        
        except Exception as e:
            self.root.after(0, lambda: self.log(f"❌ 下载出错: {e}"))
            self.root.after(0, lambda: self.update_status("下载失败"))
        
        finally:
            self.root.after(0, self.download_finished)
    
    def cancel_download(self):
        """取消下载"""
        self.is_downloading = False
        self.log("⏹️ 正在取消下载...")
        self.update_status("正在取消...")
    
    def download_finished(self):
        """下载完成"""
        self.is_downloading = False
        self.download_btn.configure(state="normal")
        self.cancel_btn.configure(state="disabled")
        if self.status_label.cget("text") == "正在取消...":
            self.update_status("已取消")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = JMComicDownloader()
        app.run()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序出错: {e}")


if __name__ == "__main__":
    main()
