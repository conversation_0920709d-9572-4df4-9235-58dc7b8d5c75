#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动脚本
自动安装依赖并运行GUI
"""

import subprocess
import sys

def install_package(package):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except:
        return False

def main():
    print("JMComic 简单下载器启动中...")
    
    # 检查并安装PyQt5
    try:
        import PyQt5
        print("✓ PyQt5 已安装")
    except ImportError:
        print("正在安装 PyQt5...")
        if install_package("PyQt5"):
            print("✓ PyQt5 安装成功")
        else:
            print("✗ PyQt5 安装失败")
            return
    
    # 检查并安装jmcomic
    try:
        import jmcomic
        print("✓ jmcomic 已安装")
    except ImportError:
        print("正在安装 jmcomic...")
        if install_package("jmcomic"):
            print("✓ jmcomic 安装成功")
        else:
            print("✗ jmcomic 安装失败")
            return
    
    # 启动GUI
    print("启动GUI...")
    try:
        from simple_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
