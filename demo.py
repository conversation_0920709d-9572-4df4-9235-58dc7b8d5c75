#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMComic GUI 演示脚本
用于演示GUI程序的功能，无需实际下载
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# 导入GUI模块
try:
    from jmcomic_gui import JMComicGUI
except ImportError as e:
    print(f"导入GUI模块失败: {e}")
    print("请确保 jmcomic_gui.py 文件在当前目录")
    sys.exit(1)


class DemoGUI(JMComicGUI):
    """演示版GUI，重写下载功能以避免实际下载"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("JMComic 漫画下载器 - 演示版")
        
        # 添加演示说明
        self.log("=" * 50)
        self.log("欢迎使用 JMComic GUI 演示版！")
        self.log("=" * 50)
        self.log("这是一个功能演示版本，不会进行实际下载")
        self.log("您可以测试所有界面功能和配置选项")
        self.log("")
        self.log("演示功能：")
        self.log("✓ 界面布局和控件")
        self.log("✓ 配置选项设置")
        self.log("✓ 输入验证")
        self.log("✓ 日志显示")
        self.log("✓ 配置保存/加载")
        self.log("✓ 菜单功能")
        self.log("")
        self.log("请输入任意漫画ID进行演示...")
        
        # 设置一些示例数据
        self.album_id_input.setText("422866, 123456")
        
        # 定时器用于模拟下载进度
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.demo_progress)
        self.demo_step = 0
    
    def start_download(self):
        """重写下载方法，进行演示而不实际下载"""
        # 验证输入
        album_ids = self.parse_album_ids(self.album_id_input.text())
        if not album_ids:
            QMessageBox.warning(self, "输入错误", "请输入有效的漫画ID")
            return
        
        download_dir = self.download_dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "输入错误", "请选择下载目录")
            return
        
        # 获取配置选项
        options = self.get_download_options()
        
        # 显示演示信息
        self.log("=" * 30)
        self.log("开始演示下载过程...")
        self.log(f"漫画ID: {', '.join(album_ids)}")
        self.log(f"下载目录: {download_dir}")
        self.log(f"客户端类型: {options['client_impl']}")
        self.log(f"图片格式: {options['image_format'] or '原格式'}")
        self.log(f"图片并发数: {options['concurrent_images']}")
        self.log(f"章节并发数: {options['concurrent_photos']}")
        self.log(f"启用缓存: {options['cache_enabled']}")
        self.log(f"图片解码: {options['decode_enabled']}")
        self.log(f"重试次数: {options['retry_times']}")
        self.log(f"文件夹规则: {options['dir_rule']}")
        self.log("=" * 30)
        
        # 更新UI状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        # 开始演示进度
        self.demo_step = 0
        self.demo_album_ids = album_ids
        self.demo_timer.start(1000)  # 每秒更新一次
    
    def demo_progress(self):
        """模拟下载进度"""
        if self.demo_step < len(self.demo_album_ids) * 3:
            album_index = self.demo_step // 3
            step_in_album = self.demo_step % 3
            
            if album_index < len(self.demo_album_ids):
                album_id = self.demo_album_ids[album_index]
                
                if step_in_album == 0:
                    self.log(f"正在获取漫画 {album_id} 的信息...")
                elif step_in_album == 1:
                    self.log(f"正在下载漫画 {album_id} 的章节...")
                else:
                    self.log(f"漫画 {album_id} 下载完成 ✓")
            
            self.demo_step += 1
        else:
            # 演示完成
            self.demo_timer.stop()
            self.on_demo_finished()
    
    def on_demo_finished(self):
        """演示完成"""
        self.log("=" * 30)
        self.log("演示下载完成！")
        self.log("在实际使用中，漫画文件会保存到指定目录")
        self.log("=" * 30)
        
        # 更新UI状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 显示完成消息
        QMessageBox.information(
            self, "演示完成", 
            f"演示下载完成！\n\n"
            f"共演示下载 {len(self.demo_album_ids)} 本漫画\n"
            f"在实际使用中，需要安装 jmcomic 库才能进行真实下载"
        )
    
    def cancel_download(self):
        """取消演示下载"""
        if self.demo_timer.isActive():
            self.demo_timer.stop()
            self.log("演示下载已取消")
            
            # 更新UI状态
            self.download_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            self.progress_bar.setVisible(False)
    
    def check_dependencies(self):
        """重写依赖检查，显示演示信息"""
        self.log("注意: 这是演示版本，不检查 jmcomic 库依赖")
        self.log("如需实际下载，请使用完整版本并安装 jmcomic 库")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("JMComic 漫画下载器 - 演示版")
    app.setApplicationVersion("1.0.0-demo")
    app.setOrganizationName("JMComic GUI Demo")
    
    # 创建并显示演示窗口
    window = DemoGUI()
    window.show()
    
    # 显示欢迎消息
    QMessageBox.information(
        window, "欢迎", 
        "欢迎使用 JMComic GUI 演示版！\n\n"
        "这是一个功能演示版本，您可以：\n"
        "• 测试所有界面功能\n"
        "• 配置下载选项\n"
        "• 查看模拟下载过程\n"
        "• 体验完整的用户界面\n\n"
        "注意：演示版不会进行实际下载"
    )
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
