# GitHub Actions 测试用
# 移动端配置
dir_rule:
  rule: Bd_Aauthor_Aid_Pindextitle

client:
  impl: api
  retry_times: 3
  postman:
    meta_data:
      timeout: 15
  domain:
    html:
      - jmcomic1.me
      - jmcomic.me

# 插件配置
plugins:
  after_init:
    - plugin: usage_log # 实时打印硬件占用率的插件
      kwargs:
        interval: 0.5 # 间隔时间
        enable_warning: false # 不告警

    - plugin: client_proxy
      kwargs:
        proxy_client_key: photo_concurrent_fetcher_proxy
        whitelist: [ api, ]