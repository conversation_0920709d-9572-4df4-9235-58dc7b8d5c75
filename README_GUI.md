# JMComic GUI 漫画下载器

基于 PyQt5 和 jmcomic 库的简单图形界面漫画下载工具。

## 功能特点

- 🎨 简洁直观的图形界面
- 📚 支持批量下载多本漫画
- ⚙️ 可配置下载选项（客户端类型、图片格式、并发数等）
- 📊 实时下载进度显示
- 📝 详细的下载日志
- 🛑 支持取消下载

## 安装依赖

### 方法一：使用 pip 安装
```bash
pip install PyQt5 jmcomic
```

### 方法二：使用 requirements.txt
```bash
pip install -r requirements.txt
```

## 使用方法

1. **启动程序**
   ```bash
   python jmcomic_gui.py
   ```

2. **输入漫画ID**
   - 在"漫画ID"输入框中输入要下载的漫画ID
   - 支持多个ID，用逗号分隔，例如：`422866, 123456, 789012`
   - ID可以包含或不包含"JM"前缀

3. **选择下载目录**
   - 点击"浏览..."按钮选择下载目录
   - 或直接在输入框中输入路径

4. **配置下载选项**
   - **客户端类型**：
     - `api (移动端)`：不限IP，兼容性好
     - `html (网页端)`：限制IP地区但效率高
   - **图片格式**：选择保存的图片格式（原格式/jpg/png/webp）
   - **并发数**：调整同时下载的图片和章节数量

5. **开始下载**
   - 点击"开始下载"按钮
   - 在下方日志区域查看下载进度
   - 可随时点击"取消下载"停止任务

## 界面说明

### 漫画信息区域
- **漫画ID输入框**：输入要下载的漫画ID
- **下载目录选择**：指定漫画保存位置

### 下载配置区域
- **客户端类型**：选择使用移动端API还是网页端
- **图片格式**：设置图片保存格式
- **图片并发数**：同时下载的图片数量（1-100）
- **章节并发数**：同时下载的章节数量（1-50）

### 控制区域
- **开始下载**：启动下载任务
- **取消下载**：停止当前下载
- **进度条**：显示下载进度

### 日志区域
- 显示详细的下载日志信息
- 包含时间戳和状态信息
- 支持清除日志功能

## 注意事项

1. **首次使用**：程序会自动检查 jmcomic 库是否安装
2. **网络环境**：确保网络连接正常，某些地区可能需要代理
3. **下载速度**：可根据网络情况调整并发数
4. **存储空间**：确保下载目录有足够的存储空间
5. **合法使用**：请遵守相关法律法规，仅用于学习和研究

## 常见问题

### Q: 提示"jmcomic库未安装"
A: 运行 `pip install jmcomic` 安装依赖库

### Q: 下载失败或速度慢
A: 尝试以下方法：
- 切换客户端类型（api/html）
- 降低并发数
- 检查网络连接
- 使用代理（需要在jmcomic配置中设置）

### Q: 如何设置代理
A: 目前GUI版本暂不支持代理设置，可以通过修改系统代理或使用jmcomic配置文件

## 技术说明

- **界面框架**：PyQt5
- **下载引擎**：jmcomic
- **多线程**：使用QThread实现异步下载
- **配置管理**：基于jmcomic的Option系统

## 项目文件说明

### 核心文件
- `jmcomic_gui.py` - 主程序文件，包含完整的GUI界面
- `start_gui.py` - 启动脚本，自动检查和安装依赖
- `demo.py` - 演示版本，无需jmcomic库即可体验界面
- `test_gui.py` - 测试脚本，包含单元测试和演示功能

### 配置文件
- `requirements.txt` - Python依赖列表
- `jmcomic_gui_config.json` - 自动生成的配置文件（首次运行后创建）

### 启动文件
- `start_gui.bat` - Windows批处理启动文件
- `README_GUI.md` - 本说明文档

## 快速开始

### 方法一：使用启动脚本（推荐）
```bash
# Windows用户
双击 start_gui.bat

# 或使用Python
python start_gui.py
```

### 方法二：直接运行
```bash
# 确保已安装依赖
pip install -r requirements.txt

# 运行主程序
python jmcomic_gui.py
```

### 方法三：体验演示版
```bash
# 无需安装jmcomic库，仅体验界面
python demo.py
```

## 高级功能

### 配置文件
程序会自动保存配置到 `jmcomic_gui_config.json`，包括：
- 下载目录设置
- 客户端类型选择
- 图片格式和并发设置
- 所有界面选项

### 快捷键
- `Ctrl+S` - 保存当前配置
- `Ctrl+O` - 重新加载配置
- `Ctrl+Q` - 退出程序

### 菜单功能
- **文件菜单**：配置保存/加载、退出
- **工具菜单**：清除日志、打开下载目录
- **帮助菜单**：使用说明、关于信息

## 开发和测试

### 运行测试
```bash
# 运行单元测试
python test_gui.py

# 运行GUI演示
python test_gui.py demo
```

### 自定义配置
可以通过修改 `jmcomic_gui_config.json` 来预设配置：
```json
{
  "download_dir": "D:/Downloads/JMComic",
  "client_type": 0,
  "image_format": 1,
  "image_concurrent": 20,
  "photo_concurrent": 8
}
```

## 版本信息

- **版本**：1.0.0
- **基于**：jmcomic 2.6.4+
- **支持**：Python 3.7+
- **界面**：PyQt5 5.15.0+

## 更新日志

### v1.0.0 (2024-08-03)
- ✨ 初始版本发布
- 🎨 完整的PyQt5图形界面
- ⚙️ 丰富的配置选项
- 📊 实时下载进度显示
- 💾 自动配置保存/加载
- 🔧 菜单栏和快捷键支持
- 🧪 演示版本和测试脚本
- 📚 完整的文档和说明
