#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMComic GUI 测试脚本
用于测试GUI程序的基本功能
"""

import sys
import unittest
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# 导入GUI模块
from jmcomic_gui import JMComicGUI, DownloadWorker


class TestJMComicGUI(unittest.TestCase):
    """GUI测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.gui = JMComicGUI()
    
    def tearDown(self):
        """每个测试后的清理"""
        self.gui.close()
    
    def test_gui_initialization(self):
        """测试GUI初始化"""
        # 检查窗口标题
        self.assertEqual(self.gui.windowTitle(), "JMComic 漫画下载器")
        
        # 检查主要组件是否存在
        self.assertIsNotNone(self.gui.album_id_input)
        self.assertIsNotNone(self.gui.download_dir_input)
        self.assertIsNotNone(self.gui.download_button)
        self.assertIsNotNone(self.gui.cancel_button)
        self.assertIsNotNone(self.gui.log_text)
    
    def test_parse_album_ids(self):
        """测试漫画ID解析功能"""
        # 测试单个ID
        ids = self.gui.parse_album_ids("422866")
        self.assertEqual(ids, ["422866"])
        
        # 测试多个ID
        ids = self.gui.parse_album_ids("422866, 123456, 789012")
        self.assertEqual(ids, ["422866", "123456", "789012"])
        
        # 测试带JM前缀的ID
        ids = self.gui.parse_album_ids("JM422866, jm123456")
        self.assertEqual(ids, ["422866", "123456"])
        
        # 测试空输入
        ids = self.gui.parse_album_ids("")
        self.assertEqual(ids, [])
        
        # 测试无效ID
        ids = self.gui.parse_album_ids("abc, 123, xyz")
        self.assertEqual(ids, ["123"])
    
    def test_get_download_options(self):
        """测试下载选项获取"""
        options = self.gui.get_download_options()
        
        # 检查选项结构
        self.assertIn('client_impl', options)
        self.assertIn('image_format', options)
        self.assertIn('concurrent_images', options)
        self.assertIn('concurrent_photos', options)
        
        # 检查默认值
        self.assertEqual(options['client_impl'], 'api')
        self.assertIsNone(options['image_format'])
        self.assertEqual(options['concurrent_images'], 30)
        self.assertEqual(options['concurrent_photos'], 16)
    
    def test_log_functionality(self):
        """测试日志功能"""
        # 清空日志
        self.gui.log_text.clear()
        
        # 添加日志消息
        test_message = "测试日志消息"
        self.gui.log(test_message)
        
        # 检查日志是否添加
        log_content = self.gui.log_text.toPlainText()
        self.assertIn(test_message, log_content)
    
    def test_ui_state_changes(self):
        """测试UI状态变化"""
        # 初始状态
        self.assertTrue(self.gui.download_button.isEnabled())
        self.assertFalse(self.gui.cancel_button.isEnabled())
        self.assertFalse(self.gui.progress_bar.isVisible())
        
        # 模拟开始下载状态（不实际下载）
        self.gui.download_button.setEnabled(False)
        self.gui.cancel_button.setEnabled(True)
        self.gui.progress_bar.setVisible(True)
        
        # 检查状态
        self.assertFalse(self.gui.download_button.isEnabled())
        self.assertTrue(self.gui.cancel_button.isEnabled())
        self.assertTrue(self.gui.progress_bar.isVisible())


class TestDownloadWorker(unittest.TestCase):
    """下载工作线程测试类"""
    
    def test_worker_initialization(self):
        """测试工作线程初始化"""
        album_ids = ["422866", "123456"]
        download_dir = "/test/path"
        options = {'client_impl': 'api'}
        
        worker = DownloadWorker(album_ids, download_dir, options)
        
        self.assertEqual(worker.album_ids, album_ids)
        self.assertEqual(worker.download_dir, download_dir)
        self.assertEqual(worker.options, options)
        self.assertFalse(worker.is_cancelled)
    
    def test_worker_cancel(self):
        """测试取消功能"""
        worker = DownloadWorker([], "", {})
        
        # 初始状态
        self.assertFalse(worker.is_cancelled)
        
        # 取消
        worker.cancel()
        self.assertTrue(worker.is_cancelled)


def run_gui_demo():
    """运行GUI演示"""
    print("启动JMComic GUI演示...")
    
    app = QApplication(sys.argv)
    
    # 创建GUI实例
    gui = JMComicGUI()
    
    # 设置一些示例数据
    gui.album_id_input.setText("422866")
    gui.log("GUI演示已启动")
    gui.log("请输入漫画ID并选择下载目录")
    gui.log("注意：需要安装jmcomic库才能正常下载")
    
    # 显示窗口
    gui.show()
    
    # 运行应用
    sys.exit(app.exec_())


def run_tests():
    """运行测试"""
    print("运行JMComic GUI测试...")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestJMComicGUI))
    suite.addTests(loader.loadTestsFromTestCase(TestDownloadWorker))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        # 运行演示
        run_gui_demo()
    else:
        # 运行测试
        success = run_tests()
        sys.exit(0 if success else 1)
