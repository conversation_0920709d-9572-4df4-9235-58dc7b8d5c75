#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动JMComic下载器
自动安装依赖并启动美观界面
"""

import subprocess
import sys

def install_package(package):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-q"])
        return True
    except:
        return False

def main():
    print("🎨 JMComic Downloader 启动中...")
    print("=" * 40)
    
    # 检查并安装依赖
    packages = [
        ("customtkinter", "现代化UI库"),
        ("jmcomic", "JMComic下载库")
    ]
    
    for package, desc in packages:
        try:
            __import__(package)
            print(f"✅ {desc} 已安装")
        except ImportError:
            print(f"📦 正在安装 {desc}...")
            if install_package(package):
                print(f"✅ {desc} 安装成功")
            else:
                print(f"❌ {desc} 安装失败")
                input("按回车键退出...")
                return
    
    print("🚀 启动界面...")
    print("=" * 40)
    
    # 启动GUI
    try:
        from beautiful_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
