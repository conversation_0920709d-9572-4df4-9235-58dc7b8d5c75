#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的JMComic下载器GUI
只需要输入ID和选择目录即可下载
"""

import sys
import os
from pathlib import Path

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog, QMessageBox
    )
    from PyQt5.QtCore import QThread, pyqtSignal
except ImportError:
    print("请先安装PyQt5: pip install PyQt5")
    sys.exit(1)

try:
    import jmcomic
except ImportError:
    print("请先安装jmcomic: pip install jmcomic")
    sys.exit(1)


class DownloadThread(QThread):
    """下载线程"""
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()
    
    def __init__(self, album_ids, download_dir):
        super().__init__()
        self.album_ids = album_ids
        self.download_dir = download_dir
        self.is_cancelled = False
    
    def run(self):
        """执行下载"""
        try:
            for album_id in self.album_ids:
                if self.is_cancelled:
                    break
                
                self.log_signal.emit(f"开始下载漫画 {album_id}...")
                
                # 创建下载配置
                option = jmcomic.JmOption.construct({
                    'dir_rule': {
                        'base_dir': self.download_dir,
                        'rule': 'Bd_Aauthor_Atitle_Pindex'
                    }
                })
                
                # 下载漫画
                jmcomic.download_album(album_id, option)
                self.log_signal.emit(f"漫画 {album_id} 下载完成 ✓")
            
            if not self.is_cancelled:
                self.log_signal.emit("所有下载任务完成！")
        
        except Exception as e:
            self.log_signal.emit(f"下载出错: {e}")
        
        finally:
            self.finished_signal.emit()
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True


class SimpleGUI(QMainWindow):
    """简单的下载器GUI"""
    
    def __init__(self):
        super().__init__()
        self.download_thread = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("JMComic 简单下载器")
        self.setGeometry(300, 300, 600, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 漫画ID输入
        id_layout = QHBoxLayout()
        id_layout.addWidget(QLabel("漫画ID:"))
        self.id_input = QLineEdit()
        self.id_input.setPlaceholderText("输入漫画ID，多个用逗号分隔，如：422866,123456")
        id_layout.addWidget(self.id_input)
        layout.addLayout(id_layout)
        
        # 下载目录选择
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("下载目录:"))
        self.dir_input = QLineEdit()
        self.dir_input.setText(str(Path.home() / "Downloads" / "JMComic"))
        dir_layout.addWidget(self.dir_input)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.browse_button)
        layout.addLayout(dir_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.download_button = QPushButton("开始下载")
        self.download_button.clicked.connect(self.start_download)
        button_layout.addWidget(self.download_button)
        
        self.cancel_button = QPushButton("取消下载")
        self.cancel_button.clicked.connect(self.cancel_download)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        # 日志显示
        layout.addWidget(QLabel("下载日志:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 显示使用说明
        self.log("=== JMComic 简单下载器 ===")
        self.log("1. 输入漫画ID（多个用逗号分隔）")
        self.log("2. 选择下载目录")
        self.log("3. 点击开始下载")
        self.log("准备就绪，请输入漫画ID...")
    
    def browse_directory(self):
        """浏览选择目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择下载目录")
        if directory:
            self.dir_input.setText(directory)
    
    def start_download(self):
        """开始下载"""
        # 获取漫画ID
        id_text = self.id_input.text().strip()
        if not id_text:
            QMessageBox.warning(self, "错误", "请输入漫画ID")
            return
        
        # 解析ID
        album_ids = []
        for id_str in id_text.split(','):
            id_str = id_str.strip()
            if id_str:
                # 移除JM前缀
                if id_str.upper().startswith('JM'):
                    id_str = id_str[2:]
                album_ids.append(id_str)
        
        if not album_ids:
            QMessageBox.warning(self, "错误", "请输入有效的漫画ID")
            return
        
        # 获取下载目录
        download_dir = self.dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "错误", "请选择下载目录")
            return
        
        # 创建目录
        try:
            Path(download_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目录: {e}")
            return
        
        # 开始下载
        self.log(f"开始下载 {len(album_ids)} 本漫画到: {download_dir}")
        
        self.download_thread = DownloadThread(album_ids, download_dir)
        self.download_thread.log_signal.connect(self.log)
        self.download_thread.finished_signal.connect(self.download_finished)
        self.download_thread.start()
        
        # 更新按钮状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
    
    def cancel_download(self):
        """取消下载"""
        if self.download_thread and self.download_thread.isRunning():
            self.download_thread.cancel()
            self.log("正在取消下载...")
    
    def download_finished(self):
        """下载完成"""
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.download_thread = None
    
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建并显示窗口
    window = SimpleGUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
