#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的JMComic下载器GUI
只需要输入ID和选择目录即可下载
"""

import sys
import os
from pathlib import Path

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog, QMessageBox,
        QFrame, QScrollArea, QProgressBar
    )
    from PyQt5.QtCore import QThread, pyqtSignal, Qt, QPropertyAnimation, QEasingCurve
    from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QPen
except ImportError:
    print("请先安装PyQt5: pip install PyQt5")
    sys.exit(1)

try:
    import jmcomic
except ImportError:
    print("请先安装jmcomic: pip install jmcomic")
    sys.exit(1)


class DownloadThread(QThread):
    """下载线程"""
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()
    
    def __init__(self, album_ids, download_dir):
        super().__init__()
        self.album_ids = album_ids
        self.download_dir = download_dir
        self.is_cancelled = False
    
    def run(self):
        """执行下载"""
        try:
            for album_id in self.album_ids:
                if self.is_cancelled:
                    break
                
                self.log_signal.emit(f"📖 开始下载漫画 {album_id}...")

                # 创建下载配置
                option = jmcomic.JmOption.construct({
                    'dir_rule': {
                        'base_dir': self.download_dir,
                        'rule': 'Bd_Aauthor_Atitle_Pindex'
                    }
                })

                # 下载漫画
                jmcomic.download_album(album_id, option)
                self.log_signal.emit(f"✅ 漫画 {album_id} 下载完成")
            
            if not self.is_cancelled:
                self.log_signal.emit("🎉 所有下载任务完成！")

        except Exception as e:
            self.log_signal.emit(f"❌ 下载出错: {e}")
        
        finally:
            self.finished_signal.emit()
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True


class ModernButton(QPushButton):
    """现代化按钮"""
    def __init__(self, text, primary=False):
        super().__init__(text)
        self.primary = primary
        self.setFixedHeight(45)
        self.setFont(QFont("Segoe UI", 10))
        self.setCursor(Qt.PointingHandCursor)
        self.update_style()

    def update_style(self):
        if self.primary:
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #667eea, stop:1 #764ba2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-weight: 600;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5a6fd8, stop:1 #6a4190);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4e5bc6, stop:1 #5e377e);
                }
                QPushButton:disabled {
                    background: #cccccc;
                    color: #666666;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background: #f8f9fa;
                    color: #495057;
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    font-weight: 500;
                    padding: 0 20px;
                }
                QPushButton:hover {
                    background: #e9ecef;
                    border-color: #dee2e6;
                }
                QPushButton:pressed {
                    background: #dee2e6;
                }
                QPushButton:disabled {
                    background: #f8f9fa;
                    color: #adb5bd;
                    border-color: #f1f3f4;
                }
            """)


class ModernLineEdit(QLineEdit):
    """现代化输入框"""
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setFixedHeight(45)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 0 15px;
                color: #495057;
            }
            QLineEdit:focus {
                border-color: #667eea;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #dee2e6;
            }
        """)


class SimpleGUI(QMainWindow):
    """现代化简约下载器GUI"""

    def __init__(self):
        super().__init__()
        self.download_thread = None
        self.init_ui()
        self.apply_modern_style()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("JMComic Downloader")
        self.setGeometry(300, 300, 800, 600)
        self.setMinimumSize(700, 500)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title_label = QLabel("JMComic Downloader")
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("简约 · 高效 · 现代化的漫画下载工具")
        subtitle_label.setFont(QFont("Segoe UI", 11))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        main_layout.addWidget(subtitle_label)

        # 输入区域
        input_frame = QFrame()
        input_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        """)
        input_layout = QVBoxLayout(input_frame)
        input_layout.setSpacing(20)
        input_layout.setContentsMargins(30, 30, 30, 30)

        # 漫画ID输入
        id_label = QLabel("漫画ID")
        id_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        id_label.setStyleSheet("color: #495057; margin-bottom: 5px;")
        input_layout.addWidget(id_label)

        self.id_input = ModernLineEdit("输入漫画ID，多个用逗号分隔，如：422866, 123456")
        input_layout.addWidget(self.id_input)

        # 下载目录
        dir_label = QLabel("下载目录")
        dir_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        dir_label.setStyleSheet("color: #495057; margin-bottom: 5px;")
        input_layout.addWidget(dir_label)

        dir_layout = QHBoxLayout()
        dir_layout.setSpacing(10)

        self.dir_input = ModernLineEdit()
        self.dir_input.setText(str(Path.home() / "Downloads" / "JMComic"))
        dir_layout.addWidget(self.dir_input)

        self.browse_button = ModernButton("浏览")
        self.browse_button.setFixedWidth(80)
        self.browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.browse_button)

        input_layout.addLayout(dir_layout)
        main_layout.addWidget(input_frame)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        self.download_button = ModernButton("开始下载", primary=True)
        self.download_button.setFixedWidth(120)
        self.download_button.clicked.connect(self.start_download)
        button_layout.addWidget(self.download_button)

        self.cancel_button = ModernButton("取消下载")
        self.cancel_button.setFixedWidth(120)
        self.cancel_button.clicked.connect(self.cancel_download)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)

        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 4px;
                background: #e9ecef;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 4px;
            }
        """)
        main_layout.addWidget(self.progress_bar)

        # 日志区域
        log_frame = QFrame()
        log_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }
        """)
        log_layout = QVBoxLayout(log_frame)
        log_layout.setContentsMargins(20, 20, 20, 20)

        log_label = QLabel("下载日志")
        log_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        log_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
        log_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                color: #495057;
            }
        """)
        log_layout.addWidget(self.log_text)

        main_layout.addWidget(log_frame)
        
        # 显示欢迎信息
        self.log("🎉 欢迎使用 JMComic Downloader")
        self.log("📝 请输入漫画ID开始下载...")
        self.log("")

    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QLabel {
                background: transparent;
            }
        """)
    
    def browse_directory(self):
        """浏览选择目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择下载目录")
        if directory:
            self.dir_input.setText(directory)
    
    def start_download(self):
        """开始下载"""
        # 获取漫画ID
        id_text = self.id_input.text().strip()
        if not id_text:
            QMessageBox.warning(self, "错误", "请输入漫画ID")
            return
        
        # 解析ID
        album_ids = []
        for id_str in id_text.split(','):
            id_str = id_str.strip()
            if id_str:
                # 移除JM前缀
                if id_str.upper().startswith('JM'):
                    id_str = id_str[2:]
                album_ids.append(id_str)
        
        if not album_ids:
            QMessageBox.warning(self, "错误", "请输入有效的漫画ID")
            return
        
        # 获取下载目录
        download_dir = self.dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "错误", "请选择下载目录")
            return
        
        # 创建目录
        try:
            Path(download_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目录: {e}")
            return
        
        # 开始下载
        self.log(f"🚀 开始下载 {len(album_ids)} 本漫画")
        self.log(f"📁 保存位置: {download_dir}")
        self.log("=" * 50)

        self.download_thread = DownloadThread(album_ids, download_dir)
        self.download_thread.log_signal.connect(self.log)
        self.download_thread.finished_signal.connect(self.download_finished)
        self.download_thread.start()

        # 更新UI状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
    
    def cancel_download(self):
        """取消下载"""
        if self.download_thread and self.download_thread.isRunning():
            self.download_thread.cancel()
            self.log("⏹️ 正在取消下载...")

    def download_finished(self):
        """下载完成"""
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.download_thread = None
        self.log("=" * 50)
        self.log("✨ 所有任务已完成！")
    
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建并显示窗口
    window = SimpleGUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
