#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化响应式JMComic下载器
真正的现代化设计 + 完美的响应式布局
"""

import sys
import os
from pathlib import Path

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("请先安装PyQt5: pip install PyQt5")
    sys.exit(1)

try:
    import jmcomic
except ImportError:
    print("请先安装jmcomic: pip install jmcomic")
    sys.exit(1)


class DownloadThread(QThread):
    """下载线程"""
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # current, total
    finished_signal = pyqtSignal()
    
    def __init__(self, album_ids, download_dir):
        super().__init__()
        self.album_ids = album_ids
        self.download_dir = download_dir
        self.is_cancelled = False
    
    def run(self):
        try:
            total = len(self.album_ids)
            for i, album_id in enumerate(self.album_ids, 1):
                if self.is_cancelled:
                    break
                
                self.log_signal.emit(f"📖 下载漫画 {album_id} ({i}/{total})")
                self.progress_signal.emit(i-1, total)
                
                option = jmcomic.JmOption.construct({
                    'dir_rule': {
                        'base_dir': self.download_dir,
                        'rule': 'Bd_Aauthor_Atitle_Pindex'
                    }
                })
                
                jmcomic.download_album(album_id, option)
                self.log_signal.emit(f"✅ 漫画 {album_id} 完成")
                self.progress_signal.emit(i, total)
            
            if not self.is_cancelled:
                self.log_signal.emit("🎉 全部下载完成！")
        
        except Exception as e:
            self.log_signal.emit(f"❌ 错误: {e}")
        
        finally:
            self.finished_signal.emit()
    
    def cancel(self):
        self.is_cancelled = True


class ModernGUI(QMainWindow):
    """现代化响应式GUI"""
    
    def __init__(self):
        super().__init__()
        self.download_thread = None
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("JMComic Downloader")
        self.setMinimumSize(500, 400)
        self.resize(800, 600)
        
        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 使用QScrollArea确保响应式
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 内容容器
        content = QWidget()
        scroll.setWidget(content)
        
        # 主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll)
        
        # 内容布局
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 头部
        self.create_header(layout)
        
        # 输入区域
        self.create_input_section(layout)
        
        # 控制按钮
        self.create_controls(layout)
        
        # 进度条
        self.create_progress(layout)
        
        # 日志区域
        self.create_log_section(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_header(self, layout):
        """创建头部"""
        header = QWidget()
        header_layout = QVBoxLayout(header)
        header_layout.setSpacing(5)
        header_layout.setContentsMargins(0, 0, 0, 20)
        
        # 主标题
        title = QLabel("JMComic Downloader")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        # 副标题
        subtitle = QLabel("现代化 · 响应式 · 高效下载")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)
        
        layout.addWidget(header)
    
    def create_input_section(self, layout):
        """创建输入区域"""
        # ID输入
        id_group = QGroupBox("漫画ID")
        id_layout = QVBoxLayout(id_group)
        
        self.id_input = QLineEdit()
        self.id_input.setPlaceholderText("输入漫画ID，多个用逗号分隔 (例: 422866, 123456)")
        self.id_input.setObjectName("input")
        id_layout.addWidget(self.id_input)
        
        layout.addWidget(id_group)
        
        # 目录选择
        dir_group = QGroupBox("下载目录")
        dir_layout = QHBoxLayout(dir_group)
        
        self.dir_input = QLineEdit()
        self.dir_input.setText(str(Path.home() / "Downloads" / "JMComic"))
        self.dir_input.setObjectName("input")
        dir_layout.addWidget(self.dir_input, 1)
        
        browse_btn = QPushButton("浏览")
        browse_btn.setObjectName("secondary_btn")
        browse_btn.clicked.connect(self.browse_directory)
        dir_layout.addWidget(browse_btn)
        
        layout.addWidget(dir_group)
    
    def create_controls(self, layout):
        """创建控制按钮"""
        controls = QWidget()
        controls_layout = QHBoxLayout(controls)
        controls_layout.setContentsMargins(0, 10, 0, 10)
        
        self.download_btn = QPushButton("开始下载")
        self.download_btn.setObjectName("primary_btn")
        self.download_btn.clicked.connect(self.start_download)
        controls_layout.addWidget(self.download_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("danger_btn")
        self.cancel_btn.clicked.connect(self.cancel_download)
        self.cancel_btn.setEnabled(False)
        controls_layout.addWidget(self.cancel_btn)
        
        controls_layout.addStretch()
        layout.addWidget(controls)
    
    def create_progress(self, layout):
        """创建进度条"""
        self.progress = QProgressBar()
        self.progress.setObjectName("progress")
        self.progress.setVisible(False)
        layout.addWidget(self.progress)
    
    def create_log_section(self, layout):
        """创建日志区域"""
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setObjectName("log")
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)  # 限制高度确保响应式
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 初始化日志
        self.log("🚀 JMComic Downloader 已就绪")
        self.log("💡 请输入漫画ID开始下载")
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #f5f5f5;
            }
            
            #title {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px 0;
            }
            
            #subtitle {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 20px;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #34495e;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px;
                background: white;
            }
            
            #input {
                padding: 12px 15px;
                border: 2px solid #ecf0f1;
                border-radius: 6px;
                font-size: 13px;
                background: white;
                selection-background-color: #3498db;
            }
            
            #input:focus {
                border-color: #3498db;
                outline: none;
            }
            
            #primary_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            
            #primary_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            
            #primary_btn:pressed {
                background: #21618c;
            }
            
            #primary_btn:disabled {
                background: #bdc3c7;
            }
            
            #secondary_btn {
                background: white;
                color: #34495e;
                border: 2px solid #ecf0f1;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            
            #secondary_btn:hover {
                background: #ecf0f1;
                border-color: #d5dbdb;
            }
            
            #danger_btn {
                background: white;
                color: #e74c3c;
                border: 2px solid #fadbd8;
                padding: 12px 30px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            
            #danger_btn:hover {
                background: #fadbd8;
            }
            
            #danger_btn:disabled {
                color: #bdc3c7;
                border-color: #ecf0f1;
            }
            
            #progress {
                border: none;
                border-radius: 4px;
                background: #ecf0f1;
                height: 8px;
            }
            
            #progress::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 4px;
            }
            
            #log {
                background: #2c3e50;
                color: #ecf0f1;
                border: none;
                border-radius: 6px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                line-height: 1.4;
            }
            
            QScrollArea {
                border: none;
                background: transparent;
            }
            
            QScrollBar:vertical {
                background: #ecf0f1;
                width: 8px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #bdc3c7;
                border-radius: 4px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #95a5a6;
            }
        """)
    
    def browse_directory(self):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择下载目录")
        if directory:
            self.dir_input.setText(directory)
    
    def start_download(self):
        """开始下载"""
        # 验证输入
        id_text = self.id_input.text().strip()
        if not id_text:
            QMessageBox.warning(self, "输入错误", "请输入漫画ID")
            return
        
        # 解析ID
        album_ids = []
        for id_str in id_text.split(','):
            id_str = id_str.strip()
            if id_str:
                if id_str.upper().startswith('JM'):
                    id_str = id_str[2:]
                album_ids.append(id_str)
        
        if not album_ids:
            QMessageBox.warning(self, "输入错误", "请输入有效的漫画ID")
            return
        
        download_dir = self.dir_input.text().strip()
        if not download_dir:
            QMessageBox.warning(self, "输入错误", "请选择下载目录")
            return
        
        # 创建目录
        try:
            Path(download_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目录: {e}")
            return
        
        # 开始下载
        self.log(f"🚀 开始下载 {len(album_ids)} 本漫画")
        self.log(f"📁 保存到: {download_dir}")
        
        self.download_thread = DownloadThread(album_ids, download_dir)
        self.download_thread.log_signal.connect(self.log)
        self.download_thread.progress_signal.connect(self.update_progress)
        self.download_thread.finished_signal.connect(self.download_finished)
        self.download_thread.start()
        
        # 更新UI
        self.download_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress.setVisible(True)
        self.progress.setRange(0, len(album_ids))
        self.progress.setValue(0)
    
    def cancel_download(self):
        """取消下载"""
        if self.download_thread and self.download_thread.isRunning():
            self.download_thread.cancel()
            self.log("⏹️ 正在取消下载...")
    
    def download_finished(self):
        """下载完成"""
        self.download_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress.setVisible(False)
        self.download_thread = None
    
    def update_progress(self, current, total):
        """更新进度"""
        self.progress.setValue(current)
    
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用现代样式
    
    window = ModernGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
