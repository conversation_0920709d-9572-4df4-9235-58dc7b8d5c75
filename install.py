#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JMComic GUI 安装脚本
自动安装所需依赖并创建桌面快捷方式
"""

import sys
import os
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """安装依赖包"""
    dependencies = ["PyQt5>=5.15.0", "jmcomic>=2.6.0"]
    
    print("正在安装依赖包...")
    for package in dependencies:
        print(f"安装 {package}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "-i", "https://pypi.org/simple"
            ])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True


def create_desktop_shortcut():
    """创建桌面快捷方式"""
    system = platform.system()
    current_dir = Path(__file__).parent.absolute()
    
    if system == "Windows":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            shortcut_path = Path(desktop) / "JMComic GUI.lnk"
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{current_dir / "start_gui.py"}"'
            shortcut.WorkingDirectory = str(current_dir)
            shortcut.IconLocation = sys.executable
            shortcut.save()
            
            print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
            return True
            
        except ImportError:
            print("⚠️ 无法创建桌面快捷方式 (缺少winshell或pywin32)")
            print("可以手动创建快捷方式指向 start_gui.py")
            return False
    
    elif system == "Linux":
        try:
            desktop_dir = Path.home() / "Desktop"
            if not desktop_dir.exists():
                desktop_dir = Path.home() / "桌面"  # 中文桌面
            
            if desktop_dir.exists():
                shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=JMComic GUI
Comment=JMComic 漫画下载器
Exec=python3 "{current_dir / "start_gui.py"}"
Icon=applications-multimedia
Path={current_dir}
Terminal=false
Categories=Graphics;Photography;
"""
                shortcut_path = desktop_dir / "JMComic-GUI.desktop"
                with open(shortcut_path, 'w', encoding='utf-8') as f:
                    f.write(shortcut_content)
                
                # 设置可执行权限
                os.chmod(shortcut_path, 0o755)
                
                print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
                return True
        except Exception as e:
            print(f"⚠️ 创建桌面快捷方式失败: {e}")
            return False
    
    elif system == "Darwin":  # macOS
        print("⚠️ macOS暂不支持自动创建快捷方式")
        print("请手动创建Automator应用或使用终端运行")
        return False
    
    return False


def create_start_script():
    """创建启动脚本"""
    system = platform.system()
    current_dir = Path(__file__).parent.absolute()
    
    if system == "Windows":
        # 创建批处理文件
        bat_content = f"""@echo off
cd /d "{current_dir}"
python start_gui.py
pause
"""
        bat_path = current_dir / "启动JMComic-GUI.bat"
        with open(bat_path, 'w', encoding='gbk') as f:
            f.write(bat_content)
        
        print(f"✅ 启动脚本已创建: {bat_path}")
        return True
    
    else:
        # 创建shell脚本
        sh_content = f"""#!/bin/bash
cd "{current_dir}"
python3 start_gui.py
"""
        sh_path = current_dir / "启动JMComic-GUI.sh"
        with open(sh_path, 'w', encoding='utf-8') as f:
            f.write(sh_content)
        
        # 设置可执行权限
        os.chmod(sh_path, 0o755)
        
        print(f"✅ 启动脚本已创建: {sh_path}")
        return True


def main():
    """主函数"""
    print("=" * 60)
    print("JMComic GUI 安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 安装依赖
    print("\n📦 安装依赖包...")
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装:")
        print("pip install PyQt5 jmcomic")
        input("按回车键退出...")
        return
    
    # 创建启动脚本
    print("\n🔧 创建启动脚本...")
    create_start_script()
    
    # 创建桌面快捷方式
    print("\n🖥️ 创建桌面快捷方式...")
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("✅ 安装完成！")
    print("=" * 60)
    print("\n启动方式：")
    print("1. 双击桌面快捷方式（如果创建成功）")
    print("2. 双击启动脚本")
    print("3. 运行: python start_gui.py")
    print("4. 体验演示版: python demo.py")
    
    print("\n注意事项：")
    print("• 首次使用建议先运行演示版熟悉界面")
    print("• 确保网络连接正常")
    print("• 遵守相关法律法规，仅用于学习研究")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
