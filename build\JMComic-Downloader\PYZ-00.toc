('E:\\娱乐项目\\build\\JMComic-Downloader\\PYZ-00.pyz',
 [('Crypto', 'E:\\python\\lib\\site-packages\\Crypto\\__init__.py', 'PYMODULE'),
  ('Crypto.Cipher',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'E:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'E:\\python\\lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'E:\\python\\lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'E:\\python\\lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Random',
   'E:\\python\\lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'E:\\python\\lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('PIL', 'E:\\python\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\python\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'E:\\python\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\python\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\python\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\python\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\python\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\python\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'E:\\python\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\python\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'E:\\python\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'E:\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'E:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'E:\\python\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'E:\\python\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\python\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support', 'E:\\python\\lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'E:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins', 'E:\\python\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'E:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'E:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'E:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'E:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'E:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'E:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'E:\\python\\lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'E:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'E:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'E:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python\\lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'E:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'E:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'E:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'E:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'E:\\python\\lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'E:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'E:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.tasks', 'E:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'E:\\python\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.transports', 'E:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'E:\\python\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'E:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bcrypt', 'E:\\python\\lib\\site-packages\\bcrypt\\__init__.py', 'PYMODULE'),
  ('bdb', 'E:\\python\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'E:\\python\\lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'E:\\python\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'E:\\python\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'E:\\python\\lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'E:\\python\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'E:\\python\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'E:\\python\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'E:\\python\\lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'E:\\python\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'E:\\python\\lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'E:\\python\\lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'E:\\python\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'E:\\python\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'E:\\python\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'E:\\python\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'E:\\python\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'E:\\python\\lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'E:\\python\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'E:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'E:\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('common', 'E:\\python\\lib\\site-packages\\common\\__init__.py', 'PYMODULE'),
  ('common.base',
   'E:\\python\\lib\\site-packages\\common\\base\\__init__.py',
   'PYMODULE'),
  ('common.base.genor',
   'E:\\python\\lib\\site-packages\\common\\base\\genor.py',
   'PYMODULE'),
  ('common.base.input_listener',
   'E:\\python\\lib\\site-packages\\common\\base\\input_listener.py',
   'PYMODULE'),
  ('common.base.logger',
   'E:\\python\\lib\\site-packages\\common\\base\\logger.py',
   'PYMODULE'),
  ('common.base.mapper',
   'E:\\python\\lib\\site-packages\\common\\base\\mapper.py',
   'PYMODULE'),
  ('common.base.multi_task',
   'E:\\python\\lib\\site-packages\\common\\base\\multi_task.py',
   'PYMODULE'),
  ('common.base.packer',
   'E:\\python\\lib\\site-packages\\common\\base\\packer.py',
   'PYMODULE'),
  ('common.base.registry',
   'E:\\python\\lib\\site-packages\\common\\base\\registry.py',
   'PYMODULE'),
  ('common.ext',
   'E:\\python\\lib\\site-packages\\common\\ext\\__init__.py',
   'PYMODULE'),
  ('common.ext.cookie_parser',
   'E:\\python\\lib\\site-packages\\common\\ext\\cookie_parser.py',
   'PYMODULE'),
  ('common.ext.iphone_client',
   'E:\\python\\lib\\site-packages\\common\\ext\\iphone_client.py',
   'PYMODULE'),
  ('common.ext.qq_email',
   'E:\\python\\lib\\site-packages\\common\\ext\\qq_email.py',
   'PYMODULE'),
  ('common.postman',
   'E:\\python\\lib\\site-packages\\common\\postman\\__init__.py',
   'PYMODULE'),
  ('common.postman.postman_api',
   'E:\\python\\lib\\site-packages\\common\\postman\\postman_api.py',
   'PYMODULE'),
  ('common.postman.postman_impl',
   'E:\\python\\lib\\site-packages\\common\\postman\\postman_impl.py',
   'PYMODULE'),
  ('common.postman.postman_proxy',
   'E:\\python\\lib\\site-packages\\common\\postman\\postman_proxy.py',
   'PYMODULE'),
  ('common.util',
   'E:\\python\\lib\\site-packages\\common\\util\\__init__.py',
   'PYMODULE'),
  ('common.util.args_util',
   'E:\\python\\lib\\site-packages\\common\\util\\args_util.py',
   'PYMODULE'),
  ('common.util.assert_util',
   'E:\\python\\lib\\site-packages\\common\\util\\assert_util.py',
   'PYMODULE'),
  ('common.util.decorator_util',
   'E:\\python\\lib\\site-packages\\common\\util\\decorator_util.py',
   'PYMODULE'),
  ('common.util.file_util',
   'E:\\python\\lib\\site-packages\\common\\util\\file_util.py',
   'PYMODULE'),
  ('common.util.image_util',
   'E:\\python\\lib\\site-packages\\common\\util\\image_util.py',
   'PYMODULE'),
  ('common.util.json_util',
   'E:\\python\\lib\\site-packages\\common\\util\\json_util.py',
   'PYMODULE'),
  ('common.util.requests_util',
   'E:\\python\\lib\\site-packages\\common\\util\\requests_util.py',
   'PYMODULE'),
  ('common.util.sys_util',
   'E:\\python\\lib\\site-packages\\common\\util\\sys_util.py',
   'PYMODULE'),
  ('common.util.time_util',
   'E:\\python\\lib\\site-packages\\common\\util\\time_util.py',
   'PYMODULE'),
  ('common.util.typing_util',
   'E:\\python\\lib\\site-packages\\common\\util\\typing_util.py',
   'PYMODULE'),
  ('concurrent', 'E:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'E:\\python\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\python\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'E:\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'E:\\python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'E:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'E:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curl_cffi',
   'E:\\python\\lib\\site-packages\\curl_cffi\\__init__.py',
   'PYMODULE'),
  ('curl_cffi.__version__',
   'E:\\python\\lib\\site-packages\\curl_cffi\\__version__.py',
   'PYMODULE'),
  ('curl_cffi._asyncio_selector',
   'E:\\python\\lib\\site-packages\\curl_cffi\\_asyncio_selector.py',
   'PYMODULE'),
  ('curl_cffi.aio',
   'E:\\python\\lib\\site-packages\\curl_cffi\\aio.py',
   'PYMODULE'),
  ('curl_cffi.const',
   'E:\\python\\lib\\site-packages\\curl_cffi\\const.py',
   'PYMODULE'),
  ('curl_cffi.curl',
   'E:\\python\\lib\\site-packages\\curl_cffi\\curl.py',
   'PYMODULE'),
  ('curl_cffi.requests',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\__init__.py',
   'PYMODULE'),
  ('curl_cffi.requests.cookies',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\cookies.py',
   'PYMODULE'),
  ('curl_cffi.requests.errors',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\errors.py',
   'PYMODULE'),
  ('curl_cffi.requests.exceptions',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\exceptions.py',
   'PYMODULE'),
  ('curl_cffi.requests.headers',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\headers.py',
   'PYMODULE'),
  ('curl_cffi.requests.impersonate',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\impersonate.py',
   'PYMODULE'),
  ('curl_cffi.requests.models',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\models.py',
   'PYMODULE'),
  ('curl_cffi.requests.session',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\session.py',
   'PYMODULE'),
  ('curl_cffi.requests.utils',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\utils.py',
   'PYMODULE'),
  ('curl_cffi.requests.websockets',
   'E:\\python\\lib\\site-packages\\curl_cffi\\requests\\websockets.py',
   'PYMODULE'),
  ('curl_cffi.utils',
   'E:\\python\\lib\\site-packages\\curl_cffi\\utils.py',
   'PYMODULE'),
  ('customtkinter',
   'E:\\python\\lib\\site-packages\\customtkinter\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   'E:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'PYMODULE'),
  ('darkdetect',
   'E:\\python\\lib\\site-packages\\darkdetect\\__init__.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   'E:\\python\\lib\\site-packages\\darkdetect\\_dummy.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   'E:\\python\\lib\\site-packages\\darkdetect\\_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   'E:\\python\\lib\\site-packages\\darkdetect\\_mac_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   'E:\\python\\lib\\site-packages\\darkdetect\\_windows_detect.py',
   'PYMODULE'),
  ('dataclasses', 'E:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'E:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'E:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'E:\\python\\lib\\dis.py', 'PYMODULE'),
  ('distutils', 'E:\\python\\lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils._msvccompiler',
   'E:\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'E:\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'E:\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd', 'E:\\python\\lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.command',
   'E:\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'E:\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'E:\\python\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'E:\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'E:\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config', 'E:\\python\\lib\\distutils\\config.py', 'PYMODULE'),
  ('distutils.core', 'E:\\python\\lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.debug', 'E:\\python\\lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.dep_util', 'E:\\python\\lib\\distutils\\dep_util.py', 'PYMODULE'),
  ('distutils.dir_util', 'E:\\python\\lib\\distutils\\dir_util.py', 'PYMODULE'),
  ('distutils.dist', 'E:\\python\\lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.errors', 'E:\\python\\lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.extension',
   'E:\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'E:\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'E:\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist', 'E:\\python\\lib\\distutils\\filelist.py', 'PYMODULE'),
  ('distutils.log', 'E:\\python\\lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils.msvc9compiler',
   'E:\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn', 'E:\\python\\lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'E:\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'E:\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util', 'E:\\python\\lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.version', 'E:\\python\\lib\\distutils\\version.py', 'PYMODULE'),
  ('distutils.versionpredicate',
   'E:\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'E:\\python\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'E:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.mime', 'E:\\python\\lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.application',
   'E:\\python\\lib\\email\\mime\\application.py',
   'PYMODULE'),
  ('email.mime.base', 'E:\\python\\lib\\email\\mime\\base.py', 'PYMODULE'),
  ('email.mime.multipart',
   'E:\\python\\lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'E:\\python\\lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text', 'E:\\python\\lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'E:\\python\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'E:\\python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'E:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'E:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'E:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'E:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'E:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'E:\\python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'E:\\python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'E:\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'E:\\python\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'E:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'E:\\python\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._adapters',
   'E:\\python\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common', 'E:\\python\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'E:\\python\\lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'E:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'E:\\python\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources',
   'E:\\python\\lib\\site-packages\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   'E:\\python\\lib\\site-packages\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('importlib_resources._common',
   'E:\\python\\lib\\site-packages\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   'E:\\python\\lib\\site-packages\\importlib_resources\\_functional.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   'E:\\python\\lib\\site-packages\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   'E:\\python\\lib\\site-packages\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   'E:\\python\\lib\\site-packages\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   'E:\\python\\lib\\site-packages\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources.future',
   'E:\\python\\lib\\site-packages\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   'E:\\python\\lib\\site-packages\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   'E:\\python\\lib\\site-packages\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('inspect', 'E:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'E:\\python\\lib\\site-packages\\jaraco\\context\\__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   'E:\\python\\lib\\site-packages\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('jmcomic',
   'E:\\python\\lib\\site-packages\\jmcomic\\__init__.py',
   'PYMODULE'),
  ('jmcomic.api',
   'E:\\python\\lib\\site-packages\\jmcomic\\api.py',
   'PYMODULE'),
  ('jmcomic.cl', 'E:\\python\\lib\\site-packages\\jmcomic\\cl.py', 'PYMODULE'),
  ('jmcomic.jm_client_impl',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_client_impl.py',
   'PYMODULE'),
  ('jmcomic.jm_client_interface',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_client_interface.py',
   'PYMODULE'),
  ('jmcomic.jm_config',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_config.py',
   'PYMODULE'),
  ('jmcomic.jm_downloader',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_downloader.py',
   'PYMODULE'),
  ('jmcomic.jm_entity',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_entity.py',
   'PYMODULE'),
  ('jmcomic.jm_exception',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_exception.py',
   'PYMODULE'),
  ('jmcomic.jm_option',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_option.py',
   'PYMODULE'),
  ('jmcomic.jm_plugin',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_plugin.py',
   'PYMODULE'),
  ('jmcomic.jm_toolkit',
   'E:\\python\\lib\\site-packages\\jmcomic\\jm_toolkit.py',
   'PYMODULE'),
  ('json', 'E:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'E:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'E:\\python\\lib\\logging\\handlers.py', 'PYMODULE'),
  ('lzma', 'E:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'E:\\python\\lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'E:\\python\\lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'E:\\python\\lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'E:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'E:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'E:\\python\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'E:\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\python\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\python\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\python\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\python\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\python\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\python\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\python\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\python\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\python\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\python\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\python\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\python\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\python\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\python\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\python\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\python\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\python\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\python\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\python\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\python\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'E:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'E:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('orjson', 'E:\\python\\lib\\site-packages\\orjson\\__init__.py', 'PYMODULE'),
  ('packaging',
   'E:\\python\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\python\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\python\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\python\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\python\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\python\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\python\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\python\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\python\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\python\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\python\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\python\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\python\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\python\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\python\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'E:\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'E:\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'E:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'E:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'E:\\python\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('psutil', 'E:\\python\\lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._common',
   'E:\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'E:\\python\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'E:\\python\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'E:\\python\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'E:\\python\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'E:\\python\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'E:\\python\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'E:\\python\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'E:\\python\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'E:\\python\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'E:\\python\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'E:\\python\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'E:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'E:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'E:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyreadline3',
   'E:\\python\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'E:\\python\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'E:\\python\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'E:\\python\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'E:\\python\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'E:\\python\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'E:\\python\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'E:\\python\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'E:\\python\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'E:\\python\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'E:\\python\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'E:\\python\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'E:\\python\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'E:\\python\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'E:\\python\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'E:\\python\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'E:\\python\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'E:\\python\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'E:\\python\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'E:\\python\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'E:\\python\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'E:\\python\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'E:\\python\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'E:\\python\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'E:\\python\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'E:\\python\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'E:\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\python\\lib\\random.py', 'PYMODULE'),
  ('readline', 'E:\\python\\lib\\site-packages\\readline.py', 'PYMODULE'),
  ('requests',
   'E:\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests_toolbelt',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt._compat',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\_compat.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\adapters\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters.source',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\adapters\\source.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters.ssl',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\adapters\\ssl.py',
   'PYMODULE'),
  ('requests_toolbelt.auth',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\auth\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.auth._digest_auth_compat',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\auth\\_digest_auth_compat.py',
   'PYMODULE'),
  ('requests_toolbelt.auth.guess',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\auth\\guess.py',
   'PYMODULE'),
  ('requests_toolbelt.auth.http_proxy_digest',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\auth\\http_proxy_digest.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\multipart\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart.decoder',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\multipart\\decoder.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart.encoder',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\multipart\\encoder.py',
   'PYMODULE'),
  ('requests_toolbelt.streaming_iterator',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\streaming_iterator.py',
   'PYMODULE'),
  ('requests_toolbelt.utils',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\utils\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.utils.user_agent',
   'E:\\python\\lib\\site-packages\\requests_toolbelt\\utils\\user_agent.py',
   'PYMODULE'),
  ('rlcompleter', 'E:\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'E:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'E:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\python\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\python\\lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'E:\\python\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\python\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\python\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\python\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\python\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\python\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\python\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\python\\lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\python\\lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\python\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\python\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\python\\lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\python\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\python\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\python\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\python\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\python\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\python\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'E:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python\\lib\\signal.py', 'PYMODULE'),
  ('simplejson',
   'E:\\python\\lib\\site-packages\\simplejson\\__init__.py',
   'PYMODULE'),
  ('simplejson.compat',
   'E:\\python\\lib\\site-packages\\simplejson\\compat.py',
   'PYMODULE'),
  ('simplejson.decoder',
   'E:\\python\\lib\\site-packages\\simplejson\\decoder.py',
   'PYMODULE'),
  ('simplejson.encoder',
   'E:\\python\\lib\\site-packages\\simplejson\\encoder.py',
   'PYMODULE'),
  ('simplejson.errors',
   'E:\\python\\lib\\site-packages\\simplejson\\errors.py',
   'PYMODULE'),
  ('simplejson.ordered_dict',
   'E:\\python\\lib\\site-packages\\simplejson\\ordered_dict.py',
   'PYMODULE'),
  ('simplejson.raw_json',
   'E:\\python\\lib\\site-packages\\simplejson\\raw_json.py',
   'PYMODULE'),
  ('simplejson.scanner',
   'E:\\python\\lib\\site-packages\\simplejson\\scanner.py',
   'PYMODULE'),
  ('site', 'E:\\python\\lib\\site.py', 'PYMODULE'),
  ('smtplib', 'E:\\python\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'E:\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'E:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'E:\\python\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'E:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'E:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python\\lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'E:\\python\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('tkinter', 'E:\\python\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'E:\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'E:\\python\\lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'E:\\python\\lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'E:\\python\\lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.font', 'E:\\python\\lib\\tkinter\\font.py', 'PYMODULE'),
  ('tkinter.messagebox', 'E:\\python\\lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.simpledialog',
   'E:\\python\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'E:\\python\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'E:\\python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('tomli', 'E:\\python\\lib\\site-packages\\tomli\\__init__.py', 'PYMODULE'),
  ('tomli._parser',
   'E:\\python\\lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re', 'E:\\python\\lib\\site-packages\\tomli\\_re.py', 'PYMODULE'),
  ('tomli._types',
   'E:\\python\\lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tracemalloc', 'E:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('trove_classifiers',
   'E:\\python\\lib\\site-packages\\trove_classifiers\\__init__.py',
   'PYMODULE'),
  ('tty', 'E:\\python\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'E:\\python\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata', 'E:\\python\\lib\\site-packages\\tzdata\\__init__.py', 'PYMODULE'),
  ('tzdata.zoneinfo',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'E:\\python\\lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest', 'E:\\python\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'E:\\python\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'E:\\python\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'E:\\python\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'E:\\python\\lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'E:\\python\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'E:\\python\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'E:\\python\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'E:\\python\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'E:\\python\\lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'E:\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'E:\\python\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'E:\\python\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'E:\\python\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'E:\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\python\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\python\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\python\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\python\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\python\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'E:\\python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'E:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('wheel', 'E:\\python\\lib\\site-packages\\wheel\\__init__.py', 'PYMODULE'),
  ('wheel.cli',
   'E:\\python\\lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'E:\\python\\lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'E:\\python\\lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'E:\\python\\lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'E:\\python\\lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'E:\\python\\lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'E:\\python\\lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util', 'E:\\python\\lib\\site-packages\\wheel\\util.py', 'PYMODULE'),
  ('wheel.vendored',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'E:\\python\\lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'E:\\python\\lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'E:\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'E:\\python\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'E:\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'E:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'E:\\python\\lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'E:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'E:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'E:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'E:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'E:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'E:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'E:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('yaml', 'E:\\python\\lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.composer',
   'E:\\python\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'E:\\python\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml', 'E:\\python\\lib\\site-packages\\yaml\\cyaml.py', 'PYMODULE'),
  ('yaml.dumper',
   'E:\\python\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'E:\\python\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error', 'E:\\python\\lib\\site-packages\\yaml\\error.py', 'PYMODULE'),
  ('yaml.events',
   'E:\\python\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'E:\\python\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes', 'E:\\python\\lib\\site-packages\\yaml\\nodes.py', 'PYMODULE'),
  ('yaml.parser',
   'E:\\python\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'E:\\python\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'E:\\python\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'E:\\python\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'E:\\python\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'E:\\python\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'E:\\python\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'E:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'E:\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp', 'E:\\python\\lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp._functools',
   'E:\\python\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'E:\\python\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'E:\\python\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'E:\\python\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat.py313',
   'E:\\python\\lib\\site-packages\\zipp\\compat\\py313.py',
   'PYMODULE'),
  ('zipp.glob', 'E:\\python\\lib\\site-packages\\zipp\\glob.py', 'PYMODULE'),
  ('zoneinfo', 'E:\\python\\lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('zoneinfo._common', 'E:\\python\\lib\\zoneinfo\\_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', 'E:\\python\\lib\\zoneinfo\\_tzpath.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo', 'E:\\python\\lib\\zoneinfo\\_zoneinfo.py', 'PYMODULE'),
  ('zstandard',
   'E:\\python\\lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'E:\\python\\lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
