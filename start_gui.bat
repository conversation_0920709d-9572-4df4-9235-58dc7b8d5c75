@echo off
chcp 65001 >nul
title JMComic GUI 漫画下载器

echo ================================================
echo JMComic GUI 漫画下载器
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境

REM 启动GUI程序
echo 🚀 启动GUI程序...
python start_gui.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    pause
)
