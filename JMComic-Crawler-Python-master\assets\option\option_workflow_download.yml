# GitHub Actions 下载脚本配置
dir_rule:
  base_dir: ${JM_DOWNLOAD_DIR}
  rule: Bd_Aauthor_Atitle_Pindex

client:
  impl: api # 使用api可免登录下载本子
  domain:
    html: [ jmcomic1.me, jmcomic.me ]

# 插件配置
plugins:
  after_init:
    - plugin: usage_log # 实时打印硬件占用率的插件
      kwargs:
        interval: 0.5 # 间隔时间
        enable_warning: false # 不告警

    - plugin: client_proxy # 提高移动端的请求效率的插件
      kwargs:
        proxy_client_key: photo_concurrent_fetcher_proxy
        whitelist: [ api, ]

    - plugin: login # 登录插件
      kwargs:
        username: ${JM_USERNAME}
        password: ${JM_PASSWORD}

  after_download: # 全部下载完成以后
    - plugin: send_qq_email # 发送邮件，如果未配置下面的前3个环境变量则不会发送。
      kwargs:
        msg_from: ${EMAIL_FROM}
        msg_to: ${EMAIL_TO}
        password: ${EMAIL_PASS}
        title: ${EMAIL_TITLE}
        content: ${EMAIL_CONTENT}
