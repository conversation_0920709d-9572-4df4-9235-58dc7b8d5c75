# JMComic Downloader 使用说明

## 🚀 快速开始

### 一键启动
```bash
python start.py
```

程序会自动：
- 检查并安装所需依赖
- 启动现代化界面
- 设置默认下载目录

## 📁 默认存储路径

**新的默认路径**: 程序所在目录下的 `downloads` 文件夹

例如：
- 如果程序在 `E:\娱乐项目\`
- 默认下载到 `E:\娱乐项目\downloads\`

这样做的好处：
- ✅ 文件就在程序旁边，方便管理
- ✅ 不会污染系统下载文件夹
- ✅ 便于备份和移动整个项目

## 📖 使用步骤

1. **启动程序**
   ```bash
   python start.py
   ```

2. **输入漫画ID**
   - 在"漫画ID"框中输入ID
   - 支持多个ID，用逗号分隔
   - 例如：`422866, 123456, 789012`

3. **选择下载目录**
   - 默认使用程序目录下的 `downloads` 文件夹
   - 可点击"浏览"按钮选择其他位置

4. **开始下载**
   - 点击"开始下载"按钮
   - 查看实时进度和日志
   - 可随时点击"取消"停止下载

## 🎨 界面版本

项目提供了多个界面版本：

### 推荐版本 (CustomTkinter)
- **文件**: `beautiful_gui.py`
- **特点**: 现代化、轻量级、美观
- **启动**: `python start.py`

### PyQt5版本
- **文件**: `modern_gui.py`
- **特点**: 功能丰富、专业界面
- **启动**: `python modern_gui.py`

### 简化版本
- **文件**: `simple_gui.py`
- **特点**: 基础功能、简单易用
- **启动**: `python simple_gui.py`

## 📂 文件结构

```
项目目录/
├── beautiful_gui.py    # 推荐的现代化界面
├── start.py           # 一键启动脚本
├── modern_gui.py      # PyQt5版本界面
├── simple_gui.py      # 简化版界面
├── downloads/         # 默认下载目录（自动创建）
├── 使用说明.md        # 本文件
└── 其他文件...
```

## ⚙️ 依赖说明

程序会自动安装以下依赖：
- `customtkinter` - 现代化UI库
- `jmcomic` - JMComic下载核心库

如果自动安装失败，可手动安装：
```bash
pip install customtkinter jmcomic
```

## 🔧 常见问题

### Q: 下载的文件在哪里？
A: 默认在程序目录下的 `downloads` 文件夹中

### Q: 如何更改下载目录？
A: 在界面中点击"浏览"按钮选择新目录

### Q: 支持哪些漫画ID格式？
A: 支持纯数字ID和JM前缀ID，例如：
- `422866` ✅
- `JM422866` ✅
- `422866, 123456` ✅（多个ID）

### Q: 下载失败怎么办？
A: 检查：
- 网络连接是否正常
- 漫画ID是否正确
- 下载目录是否有写入权限

## 📝 注意事项

- 🔒 请遵守相关法律法规
- 📚 仅用于学习和研究目的
- 🌐 确保网络连接稳定
- 💾 确保有足够的存储空间

## 🆕 更新日志

### v1.0.0
- ✨ 现代化界面设计
- 🎯 简约美观的用户体验
- 📁 默认使用程序目录存储
- 🚀 一键启动和依赖管理
- 📊 实时进度显示
- 🔄 响应式布局设计
