repo_url: https://github.com/hect0x7/JMComic-Crawler-Python
repo_name: hect0x7/JMComic-Crawler-Python
site_name: jmcomic

theme:
  palette:
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: 切换到浅色主题
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: 切换到深色主题
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: 主题跟随系统
  name: material
  features:
    - navigation.sections
    - content.code.copy
    - content.code.select
    - search.suggest
    - search.highlight
    - search.share
    - navigation.tabs
    - navigation.top
plugins:
  - search
  - mkdocstrings:
      handlers:
        # See: https://mkdocstrings.github.io/python/usage/
        python:
          paths: [ '../../src/' ]
          options:
            summary:
              functions: true
            preload_modules:
              - common
            docstring_style: sphinx


markdown_extensions:
  - attr_list
  - toc:
      permalink: true
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

docs_dir: sources